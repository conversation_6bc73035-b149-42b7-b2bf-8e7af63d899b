from aiogram.fsm.state import StatesGroup, State
from common.tests_statistics.register_handlers import get_transitions_handlers

# Расширенные состояния для менеджера с выбором персонала
class ManagerTestsStatisticsStates(StatesGroup):
    main = State()

    # Новые состояния для выбора типа персонала
    select_staff_type_for_course_entry = State()
    select_staff_type_for_month_entry = State()
    select_staff_type_for_month_control = State()
    select_staff_type_for_ent = State()

    # Состояния для выбора конкретного персонала
    select_staff_for_course_entry = State()
    select_staff_for_month_entry = State()
    select_staff_for_month_control = State()
    select_staff_for_ent = State()

    # Состояния для входного теста курса
    course_entry_select_subject = State()  # Выбор предмета (вместо группы)
    course_entry_select_user = State()     # Выбор незарегистрированного пользователя
    course_entry_result = State()          # Результат пользователя
    course_entry_result_display = State()  # Детальное отображение результата

    # Состояния для входного теста месяца
    month_entry_select_group = State()
    month_entry_select_month = State()
    month_entry_select_student = State()   # Выбор студента
    month_entry_result = State()
    month_entry_result_display = State()   # Детальное отображение результата
    month_entry_detailed_microtopics = State()  # Детальные микротемы с изображениями
    month_entry_summary_microtopics = State()   # Сводка микротем с изображениями

    # Состояния для контрольного теста месяца
    month_control_select_group = State()
    month_control_select_month = State()
    month_control_select_student = State() # Выбор студента
    month_control_result = State()
    month_control_result_display = State() # Детальное отображение результата
    month_control_detailed_microtopics = State()  # Детальные микротемы с изображениями
    month_control_summary_microtopics = State()   # Сводка микротем с изображениями

    # Состояния для пробного ЕНТ
    ent_select_group = State()
    ent_select_student = State()
    ent_result = State()
    ent_result_display = State()           # Детальное отображение результата

# Создаем словари переходов и обработчиков для менеджера
STATE_TRANSITIONS, STATE_HANDLERS = get_transitions_handlers(ManagerTestsStatisticsStates, "manager")

# Дополняем переходы для новых состояний выбора персонала
STATE_TRANSITIONS.update({
    # Переходы для выбора типа персонала (входной тест курса не использует выбор персонала)
    ManagerTestsStatisticsStates.select_staff_type_for_month_entry: ManagerTestsStatisticsStates.main,
    ManagerTestsStatisticsStates.select_staff_type_for_month_control: ManagerTestsStatisticsStates.main,
    ManagerTestsStatisticsStates.select_staff_type_for_ent: ManagerTestsStatisticsStates.main,

    # Переходы для выбора конкретного персонала (входной тест курса не использует выбор персонала)
    ManagerTestsStatisticsStates.select_staff_for_month_entry: ManagerTestsStatisticsStates.select_staff_type_for_month_entry,
    ManagerTestsStatisticsStates.select_staff_for_month_control: ManagerTestsStatisticsStates.select_staff_type_for_month_control,
    ManagerTestsStatisticsStates.select_staff_for_ent: ManagerTestsStatisticsStates.select_staff_type_for_ent,

    # Переходы для основных состояний тестов

    # Входной тест курса (без выбора персонала)
    ManagerTestsStatisticsStates.course_entry_select_subject: ManagerTestsStatisticsStates.main,
    ManagerTestsStatisticsStates.course_entry_select_user: ManagerTestsStatisticsStates.course_entry_select_subject,
    ManagerTestsStatisticsStates.course_entry_result: ManagerTestsStatisticsStates.course_entry_select_user,
    ManagerTestsStatisticsStates.course_entry_result_display: ManagerTestsStatisticsStates.course_entry_result,

    # Входной тест месяца
    ManagerTestsStatisticsStates.month_entry_select_group: ManagerTestsStatisticsStates.select_staff_for_month_entry,
    ManagerTestsStatisticsStates.month_entry_select_month: ManagerTestsStatisticsStates.month_entry_select_group,
    ManagerTestsStatisticsStates.month_entry_select_student: ManagerTestsStatisticsStates.month_entry_select_month,
    ManagerTestsStatisticsStates.month_entry_result: ManagerTestsStatisticsStates.month_entry_select_student,
    ManagerTestsStatisticsStates.month_entry_result_display: ManagerTestsStatisticsStates.month_entry_result,

    # Контрольный тест месяца
    ManagerTestsStatisticsStates.month_control_select_group: ManagerTestsStatisticsStates.select_staff_for_month_control,
    ManagerTestsStatisticsStates.month_control_select_month: ManagerTestsStatisticsStates.month_control_select_group,
    ManagerTestsStatisticsStates.month_control_select_student: ManagerTestsStatisticsStates.month_control_select_month,
    ManagerTestsStatisticsStates.month_control_result: ManagerTestsStatisticsStates.month_control_select_student,
    ManagerTestsStatisticsStates.month_control_result_display: ManagerTestsStatisticsStates.month_control_result,

    # Пробный ЕНТ
    ManagerTestsStatisticsStates.ent_select_group: ManagerTestsStatisticsStates.select_staff_for_ent,
    ManagerTestsStatisticsStates.ent_select_student: ManagerTestsStatisticsStates.ent_select_group,
    ManagerTestsStatisticsStates.ent_result: ManagerTestsStatisticsStates.ent_select_student,
    ManagerTestsStatisticsStates.ent_result_display: ManagerTestsStatisticsStates.ent_result,
})


async def manager_show_staff_type_for_month_entry(callback, state, user_role=None):
    """Показать выбор типа персонала для входного теста месяца"""
    from manager.handlers.tests import manager_select_staff_type_for_month_entry
    await manager_select_staff_type_for_month_entry(callback, state)

async def manager_show_staff_type_for_month_control(callback, state, user_role=None):
    """Показать выбор типа персонала для контрольного теста месяца"""
    from manager.handlers.tests import manager_select_staff_type_for_month_control
    await manager_select_staff_type_for_month_control(callback, state)

async def manager_show_staff_type_for_ent(callback, state, user_role=None):
    """Показать выбор типа персонала для пробного ЕНТ"""
    from manager.handlers.tests import manager_select_staff_type_for_ent
    await manager_select_staff_type_for_ent(callback, state)


async def manager_show_staff_for_month_entry(callback, state, user_role=None):
    """Показать список сотрудников для входного теста месяца"""
    from manager.keyboards.analytics import get_staff_kb

    # Получаем тип персонала из состояния
    data = await state.get_data()
    staff_type = data.get('staff_type', 'curator')  # По умолчанию куратор

    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"

    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики входного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_show_staff_for_month_control(callback, state, user_role=None):
    """Показать список сотрудников для контрольного теста месяца"""
    from manager.keyboards.analytics import get_staff_kb

    # Получаем тип персонала из состояния
    data = await state.get_data()
    staff_type = data.get('staff_type', 'curator')  # По умолчанию куратор

    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"

    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики контрольного теста месяца:",
        reply_markup=staff_kb
    )

async def manager_show_staff_for_ent(callback, state, user_role=None):
    """Показать список сотрудников для пробного ЕНТ"""
    from manager.keyboards.analytics import get_staff_kb

    # Получаем тип персонала из состояния
    data = await state.get_data()
    staff_type = data.get('staff_type', 'curator')  # По умолчанию куратор

    staff_kb = await get_staff_kb(staff_type)
    staff_name = "кураторов" if staff_type == "curator" else "преподавателей"

    await callback.message.edit_text(
        f"Выберите из списка {staff_name} для просмотра статистики пробного ЕНТ:",
        reply_markup=staff_kb
    )

# Обновляем обработчики для состояний выбора персонала
STATE_HANDLERS.update({
    # Обработчики для выбора типа персонала (входной тест курса не использует выбор персонала)
    ManagerTestsStatisticsStates.select_staff_type_for_month_entry: manager_show_staff_type_for_month_entry,
    ManagerTestsStatisticsStates.select_staff_type_for_month_control: manager_show_staff_type_for_month_control,
    ManagerTestsStatisticsStates.select_staff_type_for_ent: manager_show_staff_type_for_ent,

    # Обработчики для выбора конкретного персонала (входной тест курса не использует выбор персонала)
    ManagerTestsStatisticsStates.select_staff_for_month_entry: manager_show_staff_for_month_entry,
    ManagerTestsStatisticsStates.select_staff_for_month_control: manager_show_staff_for_month_control,
    ManagerTestsStatisticsStates.select_staff_for_ent: manager_show_staff_for_ent,
})
