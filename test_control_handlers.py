#!/usr/bin/env python3
"""
Тестовый скрипт для проверки регистрации обработчиков контрольного теста месяца
"""

def test_control_handlers_registration():
    """Тестируем регистрацию обработчиков контрольного теста месяца"""
    print("🧪 Тестирование регистрации обработчиков контрольного теста месяца...")
    
    try:
        # Импортируем состояния всех ролей
        from curator.states.states_tests import CuratorTestsStatisticsStates
        from teacher.states.states_tests import TeacherTestsStatisticsStates  
        from manager.states.states_tests import ManagerTestsStatisticsStates
        
        print("✅ Все классы состояний импортированы")
        
        # Проверяем наличие новых состояний микротем
        for role, states_class in [
            ("curator", CuratorTestsStatisticsStates),
            ("teacher", TeacherTestsStatisticsStates),
            ("manager", ManagerTestsStatisticsStates)
        ]:
            if hasattr(states_class, 'month_control_detailed_microtopics'):
                print(f"✅ {role}: month_control_detailed_microtopics найдено")
            else:
                print(f"❌ {role}: month_control_detailed_microtopics НЕ найдено")
                
            if hasattr(states_class, 'month_control_summary_microtopics'):
                print(f"✅ {role}: month_control_summary_microtopics найдено")
            else:
                print(f"❌ {role}: month_control_summary_microtopics НЕ найдено")
        
        # Проверяем, что универсальный обработчик зарегистрирован
        from common.microtopics.register_handlers import register_month_control_test_microtopics_handlers
        from aiogram import Router
        
        # Создаем тестовый роутер
        test_router = Router()
        
        def dummy_back_keyboard():
            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
            return InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="⬅️ Назад", callback_data="back")]
            ])

        # Регистрируем обработчики для куратора
        register_month_control_test_microtopics_handlers(
            router=test_router,
            states_group=CuratorTestsStatisticsStates,
            detailed_callback_prefix="curator_month_control_page",
            summary_callback_prefix="curator_month_control_summary_page",
            detailed_state=CuratorTestsStatisticsStates.month_control_detailed_microtopics,
            summary_state=CuratorTestsStatisticsStates.month_control_summary_microtopics,
            result_state=CuratorTestsStatisticsStates.month_control_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики куратора зарегистрированы")

        # Регистрируем обработчики для учителя
        register_month_control_test_microtopics_handlers(
            router=test_router,
            states_group=TeacherTestsStatisticsStates,
            detailed_callback_prefix="teacher_month_control_page",
            summary_callback_prefix="teacher_month_control_summary_page",
            detailed_state=TeacherTestsStatisticsStates.month_control_detailed_microtopics,
            summary_state=TeacherTestsStatisticsStates.month_control_summary_microtopics,
            result_state=TeacherTestsStatisticsStates.month_control_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики учителя зарегистрированы")

        # Регистрируем обработчики для менеджера
        register_month_control_test_microtopics_handlers(
            router=test_router,
            states_group=ManagerTestsStatisticsStates,
            detailed_callback_prefix="manager_month_control_page",
            summary_callback_prefix="manager_month_control_summary_page",
            detailed_state=ManagerTestsStatisticsStates.month_control_detailed_microtopics,
            summary_state=ManagerTestsStatisticsStates.month_control_summary_microtopics,
            result_state=ManagerTestsStatisticsStates.month_control_result,
            back_keyboard_func=dummy_back_keyboard
        )

        print("✅ Обработчики менеджера зарегистрированы")

        # Проверяем количество зарегистрированных обработчиков
        handlers_count = len(test_router.callback_query.handlers)
        print(f"📊 Всего зарегистрировано обработчиков: {handlers_count}")
        
        # Проверяем наличие универсального обработчика
        universal_handler_found = False
        for handler in test_router.callback_query.handlers:
            # Проверяем фильтры обработчика
            if hasattr(handler, 'filters') and handler.filters:
                for filter_obj in handler.filters:
                    if hasattr(filter_obj, 'data') and filter_obj.data == "back_to_month_control_result":
                        universal_handler_found = True
                        print("✅ Универсальный обработчик 'back_to_month_control_result' найден")
                        break
        
        if not universal_handler_found:
            print("❌ Универсальный обработчик 'back_to_month_control_result' НЕ найден")
        
        print("🎉 Тестирование завершено!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_control_handlers_registration()
