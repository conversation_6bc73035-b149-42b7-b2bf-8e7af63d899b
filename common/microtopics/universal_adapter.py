"""
Универсальный адаптер для интеграции системы отображения микротем с универсальной системой изображений
Поддерживает все роли: curator, teacher, manager
"""
import logging
from typing import Optional
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
from database.repositories.month_control_test_result_repository import MonthControlTestResultRepository
from .handlers import show_month_entry_test_microtopics_universal, show_month_control_test_microtopics_universal
from common.tests_statistics.keyboards import get_back_kb

logger = logging.getLogger(__name__)


async def convert_callback_to_student_format(
    group_id: int,
    month_test_id: int,
    student_id: int
) -> Optional[int]:
    """
    Конвертировать формат callback_data в test_result_id для использования
    в универсальной системе отображения микротем студента (входной тест месяца)

    Args:
        group_id: ID группы (из callback_data)
        month_test_id: ID теста месяца
        student_id: ID студента

    Returns:
        test_result_id для использования в системе студента или None если не найден
    """
    try:
        test_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )
        return test_result.id if test_result else None
    except Exception as e:
        logger.error(f"Ошибка при конвертации формата входного теста: {e}")
        return None


async def convert_callback_to_student_format_control(
    group_id: int,
    month_test_id: int,
    student_id: int
) -> Optional[int]:
    """
    Конвертировать формат callback_data в test_result_id для использования
    в универсальной системе отображения микротем студента (контрольный тест месяца)

    Args:
        group_id: ID группы (из callback_data)
        month_test_id: ID теста месяца
        student_id: ID студента

    Returns:
        test_result_id для использования в системе студента или None если не найден
    """
    try:
        test_result = await MonthControlTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )
        return test_result.id if test_result else None
    except Exception as e:
        logger.error(f"Ошибка при конвертации формата контрольного теста: {e}")
        return None


async def show_universal_month_entry_microtopics_with_images(
    callback: CallbackQuery,
    state: FSMContext,
    group_id: int,
    month_test_id: int, 
    student_id: int,
    target_state,
    callback_prefix: str,
    display_mode: str = "detailed"
):
    """
    Показать микротемы входного теста месяца с использованием 
    универсальной системы изображений студента
    
    Args:
        callback: Callback query
        state: FSM context
        group_id: ID группы
        month_test_id: ID теста месяца
        student_id: ID студента
        target_state: Целевое состояние для установки
        callback_prefix: Префикс для callback_data (например, "teacher_month_entry_page")
        display_mode: Режим отображения ("detailed" или "summary")
    """
    try:
        # Конвертируем формат в формат студента
        test_result_id = await convert_callback_to_student_format(
            group_id, month_test_id, student_id
        )
        
        if not test_result_id:
            await callback.message.edit_text(
                "❌ Результат теста не найден",
                reply_markup=get_back_kb()
            )
            return
        
        # Сохраняем данные для навигации назад
        await state.update_data(
            month_entry_group_id=group_id,
            month_entry_month_test_id=month_test_id,
            month_entry_student_id=student_id,
            month_entry_test_result_id=test_result_id  # Для совместимости с системой студента
        )
        
        # Определяем caption в зависимости от режима
        if display_mode == "detailed":
            caption = "📊 Детальная статистика входного теста месяца"
        else:
            caption = "💪 Сводка по входному тесту месяца"
        
        # Логируем установку состояния
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Устанавливаем состояние {target_state} для пользователя {callback.from_user.id}")

        # Используем универсальную систему отображения микротем студента
        await show_month_entry_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=target_state,
            callback_prefix=callback_prefix,
            back_keyboard_func=get_universal_back_to_result_kb,
            display_mode=display_mode,
            items_per_page=15,
            caption=caption,
            premium_check=False  # Все роли имеют доступ к статистике
        )

        # Проверяем, какое состояние установилось
        final_state = await state.get_state()
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Финальное состояние после отображения: {final_state}")

        # Добавляем задержку и проверяем состояние еще раз
        import asyncio
        await asyncio.sleep(0.1)
        final_state_after_delay = await state.get_state()
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Состояние через 0.1 сек: {final_state_after_delay}")

        if final_state != final_state_after_delay:
            logger.error(f"🚨 СОСТОЯНИЕ ИЗМЕНИЛОСЬ! {final_state} → {final_state_after_delay}")
        
    except Exception as e:
        logger.error(f"Ошибка при отображении микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении статистики микротем",
            reply_markup=get_back_kb()
        )


def get_universal_back_to_result_kb():
    """Клавиатура возврата (используем стандартную)"""
    return get_back_kb()


async def universal_back_from_month_entry_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата из изображений микротем входного теста к результату теста
    Работает для всех ролей: curator, teacher, manager
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')
        
        if not all([group_id, month_test_id, student_id]):
            logger.error("Не найдены данные для возврата к результату теста")
            try:
                # Удаляем сообщение с изображением и отправляем новое
                await callback.message.delete()
                await callback.message.answer(
                    "❌ Ошибка навигации",
                    reply_markup=get_back_kb()
                )
            except Exception:
                # Если не удалось удалить, просто отвечаем на callback
                await callback.answer("❌ Ошибка навигации")
            return
        
        # Возвращаемся к результату через существующую функцию
        from common.tests_statistics.handlers import show_month_entry_student_detail
        await show_month_entry_student_detail(callback, state, group_id, month_test_id, student_id)
        
        # Определяем роль по текущему состоянию и устанавливаем правильное состояние
        current_state = await state.get_state()
        
        if str(current_state).startswith('CuratorTestsStatisticsStates:'):
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
        elif str(current_state).startswith('TeacherTestsStatisticsStates:'):
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            await state.set_state(TeacherTestsStatisticsStates.month_entry_result)
        elif str(current_state).startswith('ManagerTestsStatisticsStates:'):
            from manager.states.states_tests import ManagerTestsStatisticsStates
            await state.set_state(ManagerTestsStatisticsStates.month_entry_result)
        else:
            # Fallback к куратору для совместимости
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
        
    except Exception as e:
        logger.error(f"Ошибка при возврате к результату теста: {e}")
        try:
            # Удаляем сообщение с изображением и отправляем новое
            await callback.message.delete()
            await callback.message.answer(
                "❌ Ошибка при возврате к результату",
                reply_markup=get_back_kb()
            )
        except Exception:
            # Если не удалось удалить, просто отвечаем на callback
            await callback.answer("❌ Ошибка при возврате к результату")


async def show_universal_month_control_test_microtopics(
    callback: CallbackQuery,
    state: FSMContext,
    group_id: int,
    month_test_id: int,
    student_id: int,
    target_state,
    callback_prefix: str,
    display_mode: str = "detailed"
):
    """
    Универсальная функция для отображения микротем контрольного теста месяца
    Работает для всех ролей: curator, teacher, manager

    Args:
        callback: Callback query
        state: FSM context
        group_id: ID группы
        month_test_id: ID теста месяца
        student_id: ID студента
        target_state: Целевое состояние для установки
        callback_prefix: Префикс для callback_data (например, "teacher_month_control_page")
        display_mode: Режим отображения ("detailed" или "summary")
    """
    try:
        # Конвертируем формат в формат студента
        test_result_id = await convert_callback_to_student_format_control(
            group_id, month_test_id, student_id
        )

        if not test_result_id:
            await callback.message.edit_text(
                "❌ Результат контрольного теста не найден",
                reply_markup=get_back_kb()
            )
            return

        # Сохраняем данные для навигации назад
        await state.update_data(
            month_control_group_id=group_id,
            month_control_month_test_id=month_test_id,
            month_control_student_id=student_id,
            month_control_test_result_id=test_result_id  # Для совместимости с системой студента
        )

        # Определяем caption в зависимости от режима
        if display_mode == "detailed":
            caption = "📊 Детальная статистика контрольного теста месяца"
        else:
            caption = "💪 Сводка по контрольному тесту месяца"

        # Устанавливаем состояние
        await state.set_state(target_state)

        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР КОНТРОЛЬНОГО ТЕСТА: Устанавливаем состояние {target_state} для пользователя {callback.from_user.id}")

        # Используем универсальную систему отображения микротем студента
        await show_month_control_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=target_state,
            callback_prefix=callback_prefix,
            back_keyboard_func=get_universal_back_to_result_kb,
            display_mode=display_mode,
            items_per_page=15,
            caption=caption,
            premium_check=False  # Все роли имеют доступ к статистике
        )

    except Exception as e:
        logger.error(f"Ошибка в универсальном адаптере контрольного теста: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении статистики",
            reply_markup=get_back_kb()
        )


async def universal_back_from_month_control_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата из изображений микротем контрольного теста к результату теста
    Работает для всех ролей: curator, teacher, manager
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_control_group_id')
        month_test_id = data.get('month_control_month_test_id')
        student_id = data.get('month_control_student_id')

        if not all([group_id, month_test_id, student_id]):
            logger.error("Не найдены данные для возврата к результату контрольного теста")
            try:
                # Удаляем сообщение с изображением и отправляем новое
                await callback.message.delete()
                await callback.message.answer(
                    "❌ Ошибка навигации",
                    reply_markup=get_back_kb()
                )
            except Exception:
                # Если не удалось удалить, просто отвечаем на callback
                await callback.answer("❌ Ошибка навигации")
            return

        # Возвращаемся к результату через функцию отображения статистики как у студента
        await show_month_control_test_statistics_like_student(callback, state, group_id, month_test_id, student_id)

        # Определяем роль по текущему состоянию и устанавливаем правильное состояние
        current_state = await state.get_state()

        if str(current_state).startswith('CuratorTestsStatisticsStates:'):
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_control_result)
        elif str(current_state).startswith('TeacherTestsStatisticsStates:'):
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            await state.set_state(TeacherTestsStatisticsStates.month_control_result)
        elif str(current_state).startswith('ManagerTestsStatisticsStates:'):
            from manager.states.states_tests import ManagerTestsStatisticsStates
            await state.set_state(ManagerTestsStatisticsStates.month_control_result)
        else:
            # Fallback к куратору для совместимости
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_control_result)

        logger.info(f"🔙 Возврат к результату контрольного теста: group_id={group_id}, month_test_id={month_test_id}, student_id={student_id}")

    except Exception as e:
        logger.error(f"Ошибка при возврате к результату контрольного теста: {e}")
        try:
            await callback.message.delete()
            await callback.message.answer(
                "❌ Ошибка навигации",
                reply_markup=get_back_kb()
            )
        except Exception:
            await callback.answer("❌ Ошибка навигации")


async def show_month_control_test_statistics_like_student(callback: CallbackQuery, state: FSMContext, group_id: int, month_test_id: int, student_id: int):
    """
    Показать статистику контрольного теста месяца в формате как у студента
    Включает сравнение с входным тестом и кнопки аналитики
    """
    try:
        from database import (
            MonthControlTestResultRepository, MonthEntryTestResultRepository,
            StudentRepository, GroupRepository, MonthTestRepository
        )
        from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup

        # Получаем результат контрольного теста
        control_result = await MonthControlTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )

        if not control_result:
            await callback.message.edit_text(
                "❌ Результат контрольного теста не найден",
                reply_markup=get_back_kb()
            )
            return

        # Получаем данные о студенте, группе и тесте
        student = await StudentRepository.get_by_id(student_id)
        group = await GroupRepository.get_by_id(group_id)
        month_test = await MonthTestRepository.get_by_id(month_test_id)

        if not all([student, group, month_test]):
            await callback.message.edit_text(
                "❌ Данные не найдены",
                reply_markup=get_back_kb()
            )
            return

        # Пытаемся найти соответствующий входной результат для сравнения
        comparison_text = ""
        entry_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )

        if entry_result:
            # Показываем сравнение с входным тестом (как у студента)
            comparison_text = f"\n📊 Сравнение с входным тестом:\n"
            comparison_text += f"Верных: {entry_result.correct_answers}/{entry_result.total_questions} → {control_result.correct_answers}/{control_result.total_questions}\n"

            # Рассчитываем общий рост
            if entry_result.score_percentage > 0:
                growth = ((control_result.score_percentage - entry_result.score_percentage) / entry_result.score_percentage) * 100
                if growth > 0:
                    comparison_text += f"📈 Общий рост: +{growth:.1f}%\n"
                elif growth < 0:
                    comparison_text += f"📉 Общее снижение: {growth:.1f}%\n"
                else:
                    comparison_text += f"📊 Результат остался на том же уровне\n"
            else:
                if control_result.score_percentage > 0:
                    comparison_text += f"📈 Рост: +{control_result.score_percentage:.1f}%\n"

        # Формируем результат (как у студента)
        result_text = f"🎉 Контрольный тест месяца\n\n"
        result_text += f"👤 Студент: {student.user.name}\n"
        result_text += f"📗 {month_test.subject.name}\n"
        result_text += f"📝 Тест: {month_test.name}\n"
        result_text += f"Верных ответов: {control_result.correct_answers} / {control_result.total_questions}\n"
        result_text += f"Процент: {control_result.score_percentage}%\n"
        result_text += comparison_text
        result_text += "\nВыберите тип аналитики:"

        # Создаем кнопки для детальной аналитики (как у студента)
        buttons = [
            [InlineKeyboardButton(
                text="📊 Проценты по микротемам",
                callback_data=f"month_control_detailed_{group_id}_{month_test_id}_{student_id}"
            )],
            [InlineKeyboardButton(
                text="💪 Сильные/слабые темы",
                callback_data=f"month_control_summary_{group_id}_{month_test_id}_{student_id}"
            )]
        ]
        buttons.extend(get_back_kb().inline_keyboard)

        await callback.message.edit_text(
            result_text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
        )

        # Сохраняем данные для навигации
        await state.update_data(
            month_control_group_id=group_id,
            month_control_month_test_id=month_test_id,
            month_control_student_id=student_id,
            month_control_test_result_id=control_result.id
        )

        logger.info(f"Показана статистика контрольного теста как у студента для пользователя {callback.from_user.id}")

    except Exception as e:
        logger.error(f"Ошибка при показе статистики контрольного теста: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении статистики",
            reply_markup=get_back_kb()
        )
