"""
Универсальный адаптер для интеграции системы отображения микротем с универсальной системой изображений
Поддерживает все роли: curator, teacher, manager
"""
import logging
from typing import Optional
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
from .handlers import show_month_entry_test_microtopics_universal
from common.tests_statistics.keyboards import get_back_kb

logger = logging.getLogger(__name__)


async def convert_callback_to_student_format(
    group_id: int, 
    month_test_id: int, 
    student_id: int
) -> Optional[int]:
    """
    Конвертировать формат callback_data в test_result_id для использования 
    в универсальной системе отображения микротем студента
    
    Args:
        group_id: ID группы (из callback_data)
        month_test_id: ID теста месяца
        student_id: ID студента
        
    Returns:
        test_result_id для использования в системе студента или None если не найден
    """
    try:
        test_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(
            student_id, month_test_id
        )
        return test_result.id if test_result else None
    except Exception as e:
        logger.error(f"Ошибка при конвертации формата: {e}")
        return None


async def show_universal_month_entry_microtopics_with_images(
    callback: CallbackQuery,
    state: FSMContext,
    group_id: int,
    month_test_id: int, 
    student_id: int,
    target_state,
    callback_prefix: str,
    display_mode: str = "detailed"
):
    """
    Показать микротемы входного теста месяца с использованием 
    универсальной системы изображений студента
    
    Args:
        callback: Callback query
        state: FSM context
        group_id: ID группы
        month_test_id: ID теста месяца
        student_id: ID студента
        target_state: Целевое состояние для установки
        callback_prefix: Префикс для callback_data (например, "teacher_month_entry_page")
        display_mode: Режим отображения ("detailed" или "summary")
    """
    try:
        # Конвертируем формат в формат студента
        test_result_id = await convert_callback_to_student_format(
            group_id, month_test_id, student_id
        )
        
        if not test_result_id:
            await callback.message.edit_text(
                "❌ Результат теста не найден",
                reply_markup=get_back_kb()
            )
            return
        
        # Сохраняем данные для навигации назад
        await state.update_data(
            month_entry_group_id=group_id,
            month_entry_month_test_id=month_test_id,
            month_entry_student_id=student_id,
            month_entry_test_result_id=test_result_id  # Для совместимости с системой студента
        )
        
        # Определяем caption в зависимости от режима
        if display_mode == "detailed":
            caption = "📊 Детальная статистика входного теста месяца"
        else:
            caption = "💪 Сводка по входному тесту месяца"
        
        # Логируем установку состояния
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Устанавливаем состояние {target_state} для пользователя {callback.from_user.id}")

        # Используем универсальную систему отображения микротем студента
        await show_month_entry_test_microtopics_universal(
            callback=callback,
            state=state,
            test_result_id=test_result_id,
            target_state=target_state,
            callback_prefix=callback_prefix,
            back_keyboard_func=get_universal_back_to_result_kb,
            display_mode=display_mode,
            items_per_page=15,
            caption=caption,
            premium_check=False  # Все роли имеют доступ к статистике
        )

        # Проверяем, какое состояние установилось
        final_state = await state.get_state()
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Финальное состояние после отображения: {final_state}")

        # Добавляем задержку и проверяем состояние еще раз
        import asyncio
        await asyncio.sleep(0.1)
        final_state_after_delay = await state.get_state()
        logger.info(f"🔧 УНИВЕРСАЛЬНЫЙ АДАПТЕР: Состояние через 0.1 сек: {final_state_after_delay}")

        if final_state != final_state_after_delay:
            logger.error(f"🚨 СОСТОЯНИЕ ИЗМЕНИЛОСЬ! {final_state} → {final_state_after_delay}")
        
    except Exception as e:
        logger.error(f"Ошибка при отображении микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при получении статистики микротем",
            reply_markup=get_back_kb()
        )


def get_universal_back_to_result_kb():
    """Клавиатура возврата (используем стандартную)"""
    return get_back_kb()


async def universal_back_from_month_entry_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата из изображений микротем входного теста к результату теста
    Работает для всех ролей: curator, teacher, manager
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('month_entry_group_id')
        month_test_id = data.get('month_entry_month_test_id')
        student_id = data.get('month_entry_student_id')
        
        if not all([group_id, month_test_id, student_id]):
            logger.error("Не найдены данные для возврата к результату теста")
            try:
                # Удаляем сообщение с изображением и отправляем новое
                await callback.message.delete()
                await callback.message.answer(
                    "❌ Ошибка навигации",
                    reply_markup=get_back_kb()
                )
            except Exception:
                # Если не удалось удалить, просто отвечаем на callback
                await callback.answer("❌ Ошибка навигации")
            return
        
        # Возвращаемся к результату через существующую функцию
        from common.tests_statistics.handlers import show_month_entry_student_detail
        await show_month_entry_student_detail(callback, state, group_id, month_test_id, student_id)
        
        # Определяем роль по текущему состоянию и устанавливаем правильное состояние
        current_state = await state.get_state()
        
        if str(current_state).startswith('CuratorTestsStatisticsStates:'):
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
        elif str(current_state).startswith('TeacherTestsStatisticsStates:'):
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            await state.set_state(TeacherTestsStatisticsStates.month_entry_result)
        elif str(current_state).startswith('ManagerTestsStatisticsStates:'):
            from manager.states.states_tests import ManagerTestsStatisticsStates
            await state.set_state(ManagerTestsStatisticsStates.month_entry_result)
        else:
            # Fallback к куратору для совместимости
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.month_entry_result)
        
    except Exception as e:
        logger.error(f"Ошибка при возврате к результату теста: {e}")
        try:
            # Удаляем сообщение с изображением и отправляем новое
            await callback.message.delete()
            await callback.message.answer(
                "❌ Ошибка при возврате к результату",
                reply_markup=get_back_kb()
            )
        except Exception:
            # Если не удалось удалить, просто отвечаем на callback
            await callback.answer("❌ Ошибка при возврате к результату")
