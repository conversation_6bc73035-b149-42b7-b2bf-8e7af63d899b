from aiogram.types import CallbackQuery, BufferedInputFile, InputMediaPhoto
from aiogram.fsm.context import FSMContext
from common.image_utils import generate_microtopics_table_image
from typing import Dict


async def show_detailed_microtopics_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_microtopics_page", "curator_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    title: str,                      # Готовый текст заголовка
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Детальная статистика по микротемам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "detailed_analytics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения детальной статистики по микротемам
    Извлекает student_id и subject_id из callback_data

    Формат callback_data: microtopics_detailed_STUDENT_ID_SUBJECT_ID
    """
    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            student_id = int(parts[2])
            subject_id = int(parts[3])

            await show_detailed_microtopics_universal(
                callback=callback,
                state=state,
                student_id=student_id,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                title=title,
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check,
                premium_feature=premium_feature
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_summary_microtopics_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_summary_page", "curator_summary_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    title: str,                      # Готовый текст заголовка
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "advanced_statistics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения сводки по сильным и слабым темам
    Извлекает student_id и subject_id из callback_data

    Формат callback_data: microtopics_summary_STUDENT_ID_SUBJECT_ID
    """
    # Извлекаем student_id и subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            student_id = int(parts[2])
            subject_id = int(parts[3])

            await show_summary_microtopics_universal(
                callback=callback,
                state=state,
                student_id=student_id,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                title=title,
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check,
                premium_feature=premium_feature
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_detailed_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    student_id: int,                 # ID студента
    subject_id: int,                 # ID предмета
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_microtopics_page", "curator_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    title: str,                      # Заголовок
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Детальная статистика по микротемам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "detailed_analytics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения детальной статистики по микротемам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        student_id: ID студента
        subject_id: ID предмета
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
        premium_feature: Какая премиум функция
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                feature_info = PREMIUM_FEATURES[premium_feature]
                await show_premium_required_message(
                    callback,
                    feature_info["name"],
                    feature_info["description"]
                )
                return

        # Получаем данные студента и предмета
        from database import StudentRepository, SubjectRepository, MicrotopicRepository

        student = await StudentRepository.get_by_id(student_id)
        subject = await SubjectRepository.get_by_id(subject_id)

        if not student or not subject:
            await callback.message.edit_text(
                "❌ Студент или предмет не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Получаем данные по микротемам
        microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        if not microtopic_data:
            await callback.message.edit_text(
                f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания по микротемам предмета {subject.name}",
                reply_markup=back_keyboard_func()
            )
            return


        # Получаем имя студента для заголовка
        from database.repositories import StudentRepository
        student = await StudentRepository.get_by_id(student_id)
        student_name = student.user.name if student else "Неизвестный студент"

        # Формируем динамический заголовок с именем студента
        dynamic_title = f"📊 Статистика студента {student_name}\n📈 % понимания по микротемам"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=dynamic_title,
            display_mode="detailed",
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename="microtopics_detailed.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние детальной статистики
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode="detailed",
            role=role,
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID студента и предмета для возврата
            selected_student_id=student_id,
            selected_subject_id=subject_id
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def show_summary_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    student_id: int,                 # ID студента
    subject_id: int,                 # ID предмета
    role: str,                       # "student", "curator", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "student_summary_page", "curator_summary_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    title: str,                      # Заголовок
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам",
    premium_check: bool = True,      # Нужна ли проверка премиума
    premium_feature: str = "advanced_statistics"  # Какая премиум функция
):
    """
    Универсальная функция для отображения сводки по сильным и слабым темам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        student_id: ID студента
        subject_id: ID предмета
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
        premium_feature: Какая премиум функция
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message, PREMIUM_FEATURES

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                feature_info = PREMIUM_FEATURES[premium_feature]
                await show_premium_required_message(
                    callback,
                    feature_info["name"],
                    feature_info["description"]
                )
                return

        # Получаем данные студента и предмета
        from database import StudentRepository, SubjectRepository, MicrotopicRepository

        student = await StudentRepository.get_by_id(student_id)
        subject = await SubjectRepository.get_by_id(subject_id)

        if not student or not subject:
            await callback.message.edit_text(
                "❌ Студент или предмет не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Получаем данные по микротемам
        microtopic_data = await StudentRepository.get_microtopic_understanding(student_id, subject_id)
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        if not microtopic_data:
            await callback.message.edit_text(
                f"📌 {student.user.name}\n❌ Пока не выполнено ни одного задания для анализа сильных и слабых тем по предмету {subject.name}",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем динамический заголовок с именем студента
        dynamic_title = f"📊 Статистика студента {student.user.name}\n🟢🔴 Сильные и слабые темы"

        # Генерируем изображение таблицы (режим сводки с пагинацией)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=dynamic_title,
            display_mode="summary",
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename="microtopics_summary.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние сводки
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode="summary",
            role=role,
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID студента и предмета для возврата
            selected_student_id=student_id,
            selected_subject_id=subject_id
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения сводки микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке сводки",
            reply_markup=back_keyboard_func()
        )


async def handle_microtopics_pagination_universal(
    callback: CallbackQuery,
    state: FSMContext,
    callback_prefix: str,            # "student_microtopics_page"
    display_mode: str,               # "detailed" или "summary"
    role: str                        # "student", "curator", etc.
):
    """
    Универсальная функция для обработки пагинации микротем
    
    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        callback_prefix: Префикс callback для извлечения номера страницы
        display_mode: Режим отображения ("detailed" или "summary")
        role: Роль пользователя
    """
    try:
        print(f"🔥 ПАГИНАЦИЯ: callback={callback.data}, prefix={callback_prefix}")
        # Извлекаем номер страницы из callback_data
        page_str = callback.data.split(f"{callback_prefix}_")[1]
        new_page = int(page_str)
        print(f"🔥 ПАГИНАЦИЯ: page_str={page_str}, new_page={new_page}")

        # Получаем сохраненные данные
        data = await state.get_data()
        microtopic_data = data.get('microtopic_data')
        microtopic_names = data.get('microtopic_names')
        title = data.get('display_title')
        items_per_page = data.get('items_per_page', 15)
        total_items = data.get('total_items')
        caption = data.get('caption', "📊 Статистика по микротемам")

        if not microtopic_data or not microtopic_names:
            await callback.answer("❌ Ошибка: данные не найдены")
            return

        # Генерируем изображение для новой страницы
        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode=display_mode,
            data_source=role,
            page=new_page,
            items_per_page=items_per_page
        )

        # Обновляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"microtopics_{display_mode}_page_{new_page}.png")

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.edit_media(
            media=InputMediaPhoto(media=photo),
            reply_markup=get_microtopics_pagination_kb(
                current_page=new_page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Обновляем текущую страницу в состоянии
        await state.update_data(current_page=new_page)

        await callback.answer()

    except Exception as e:
        print(f"Ошибка при обработке пагинации микротем: {e}")
        await callback.answer("❌ Ошибка при переключении страницы")


async def show_group_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    group_id: int,                   # ID группы
    role: str,                       # "curator", "teacher", "manager"
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "curator_group_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    title: str,                      # Заголовок
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Статистика группы по микротемам",
    premium_check: bool = False      # Проверка премиума
):
    """
    Универсальная функция для отображения статистики группы по микротемам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        group_id: ID группы
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        title: Заголовок
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                await show_premium_required_message(callback, "detailed_analytics")
                return

        # Получаем данные группы
        from database.repositories import GroupRepository, StudentRepository, MicrotopicRepository

        group = await GroupRepository.get_by_id(group_id)
        if not group or not group.subject:
            await callback.message.edit_text(
                "❌ Группа не найдена или не имеет предмета",
                reply_markup=back_keyboard_func()
            )
            return

        # Получаем студентов группы
        students = await StudentRepository.get_by_group(group_id)
        if not students:
            await callback.message.edit_text(
                f"👥 Группа: {group.name}\n📗 Предмет: {group.subject.name}\n\n❌ В группе нет студентов",
                reply_markup=back_keyboard_func()
            )
            return

        # Собираем статистику по микротемам
        topics_stats = {}
        for student in students:
            microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, group.subject.id)
            for microtopic_number, stats in microtopic_stats.items():
                if microtopic_number not in topics_stats:
                    topics_stats[microtopic_number] = []
                topics_stats[microtopic_number].append(stats['percentage'])

        if not topics_stats:
            await callback.message.edit_text(
                f"👥 Группа: {group.name}\n📗 Предмет: {group.subject.name}\n\n❌ Пока нет данных по микротемам",
                reply_markup=back_keyboard_func()
            )
            return

        # Вычисляем средние значения
        microtopic_data = {}
        for microtopic_number, percentages in topics_stats.items():
            if percentages:
                microtopic_data[microtopic_number] = round(sum(percentages) / len(percentages), 1)

        # Получаем названия микротем
        microtopics = await MicrotopicRepository.get_by_subject(group.subject.id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        # Формируем заголовок с названием группы
        dynamic_title = f"👥 Группа {group.name}\n📗 {group.subject.name}\n📈 Средний % понимания по микротемам"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=dynamic_title,
            display_mode="detailed",
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename="group_microtopics_detailed.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode="detailed",
            role=role,
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID группы для возврата
            selected_group_id=group_id
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем группы: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def back_from_group_microtopics_to_analytics(callback: CallbackQuery, state: FSMContext, role: str):
    """
    Универсальная функция возврата из изображений микротем группы к статистике группы

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        role: Роль пользователя
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        group_id = data.get('selected_group_id')

        if not group_id:
            # Если нет сохраненного ID группы, возвращаемся к списку групп
            from common.analytics.handlers import select_group_for_group_analytics
            await select_group_for_group_analytics(callback, state, role)
            return

        # Удаляем сообщение с изображением
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        # Получаем данные группы и формируем сообщение напрямую
        from common.statistics import get_group_stats
        from common.analytics.keyboards import get_group_analytics_kb

        group_data = await get_group_stats(str(group_id))

        # Формируем базовую информацию о группе
        result_text = f"👥 Группа: {group_data['name']}\n"
        result_text += f"📗 Предмет: {group_data['subject']}\n"
        result_text += f"📊 Средний % выполнения ДЗ: {group_data['homework_completion']}%\n"

        # Добавляем информацию о росте по тест-отчетам
        if group_data.get('test_reports_growth') is not None:
            growth = group_data['test_reports_growth']
            if growth >= 0:
                result_text += f"📈 Средний рост по тест-отчетам: +{growth}%\n"
            else:
                result_text += f"📉 Средний рост по тест-отчетам: {growth}%\n"
        else:
            result_text += f"📊 Рост по тест-отчетам: Н/Д\n"

        result_text += "\nВыберите, что хотите посмотреть:"

        # Отправляем новое сообщение
        await callback.message.answer(
            result_text,
            reply_markup=get_group_analytics_kb(int(group_id))
        )

        # Устанавливаем правильное состояние в зависимости от роли
        if role == "curator":
            from curator.handlers.analytics import CuratorAnalyticsStates
            await state.set_state(CuratorAnalyticsStates.group_stats)
        elif role == "teacher":
            from teacher.handlers.analytics import TeacherAnalyticsStates
            await state.set_state(TeacherAnalyticsStates.group_stats)
        elif role == "manager":
            from manager.handlers.analytics import ManagerAnalyticsStates
            await state.set_state(ManagerAnalyticsStates.group_stats)

    except Exception as e:
        print(f"Ошибка при возврате из микротем группы: {e}")
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        from common.analytics.keyboards import get_analytics_menu_kb
        await callback.message.answer(
            "📊 Аналитика\n\nВыберите тип статистики:",
            reply_markup=get_analytics_menu_kb(role)
        )

        # Устанавливаем главное состояние аналитики
        if role == "curator":
            from curator.handlers.analytics import CuratorAnalyticsStates
            await state.set_state(CuratorAnalyticsStates.main)
        elif role == "teacher":
            from teacher.handlers.analytics import TeacherAnalyticsStates
            await state.set_state(TeacherAnalyticsStates.main)
        elif role == "manager":
            from manager.handlers.analytics import ManagerAnalyticsStates
            await state.set_state(ManagerAnalyticsStates.main)


async def show_microtopics_with_pagination(
    callback: CallbackQuery,
    state: FSMContext,
    microtopic_data: Dict,           # Готовые данные микротем {номер: процент}
    microtopic_names: Dict[int, str], # Названия микротем {номер: название}
    title: str,                      # Заголовок
    role: str,                       # Роль для data_source
    target_state,                    # Состояние для установки
    callback_prefix: str,            # Префикс для пагинации
    display_mode: str = "detailed",  # "detailed" или "summary"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Статистика по микротемам",
    premium_check: bool = False      # Проверка премиума
):
    """
    Универсальная функция для отображения микротем с пагинацией
    Принимает готовые данные микротем без привязки к студенту/группе/предмету

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        microtopic_data: Готовые данные микротем {номер: процент}
        microtopic_names: Названия микротем {номер: название}
        title: Заголовок изображения
        role: Роль пользователя для data_source
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        display_mode: Режим отображения ("detailed" или "summary")
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                await show_premium_required_message(callback, "detailed_analytics")
                return

        # Проверяем наличие данных
        if not microtopic_data or not microtopic_names:
            await callback.message.edit_text(
                "❌ Данные по микротемам не найдены",
                reply_markup=None
            )
            return

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=title,
            display_mode=display_mode,
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"microtopics_{display_mode}.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode=display_mode,
            role=role,
            callback_prefix=callback_prefix,
            caption=caption
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=None
        )


async def get_group_microtopics_data(group_id: int) -> tuple:
    """
    Получить данные микротем группы в формате для универсальной функции

    Args:
        group_id: ID группы

    Returns:
        tuple: (microtopic_data, microtopic_names, group_name, subject_name)
               microtopic_data: {номер: процент}
               microtopic_names: {номер: название}
    """
    try:
        from database.repositories import GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем группу
        group = await GroupRepository.get_by_id(group_id)
        if not group or not group.subject:
            return {}, {}, "Неизвестная группа", "Неизвестный предмет"

        # Получаем студентов группы
        students = await StudentRepository.get_by_group(group_id)
        if not students:
            return {}, {}, group.name, group.subject.name

        # Собираем статистику по микротемам
        topics_stats = {}
        for student in students:
            microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, group.subject.id)
            for microtopic_number, stats in microtopic_stats.items():
                if microtopic_number not in topics_stats:
                    topics_stats[microtopic_number] = []
                topics_stats[microtopic_number].append(stats['percentage'])

        # Вычисляем средние значения по номерам микротем
        microtopic_data = {}
        for microtopic_number, percentages in topics_stats.items():
            if percentages:
                microtopic_data[microtopic_number] = round(sum(percentages) / len(percentages), 1)

        # Получаем названия микротем
        microtopics = await MicrotopicRepository.get_by_subject(group.subject.id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        return microtopic_data, microtopic_names, group.name, group.subject.name

    except Exception as e:
        print(f"Ошибка при получении данных микротем группы: {e}")
        return {}, {}, "Ошибка загрузки", "Ошибка загрузки"


async def get_subject_microtopics_data(subject_id: int) -> tuple:
    """
    Получить данные микротем предмета в формате для универсальной функции

    Args:
        subject_id: ID предмета

    Returns:
        tuple: (microtopic_data, microtopic_names, subject_name)
               microtopic_data: {номер: процент}
               microtopic_names: {номер: название}
    """
    try:
        from database.repositories import SubjectRepository, GroupRepository, StudentRepository, MicrotopicRepository

        # Получаем предмет
        subject = await SubjectRepository.get_by_id(subject_id)
        if not subject:
            return {}, {}, "Неизвестный предмет"

        # Получаем группы предмета
        groups = await GroupRepository.get_by_subject(subject_id)
        if not groups:
            return {}, {}, subject.name

        # Собираем статистику по всем микротемам предмета
        topics_stats = {}
        for group in groups:
            students = await StudentRepository.get_by_group(group.id)
            for student in students:
                microtopic_stats = await StudentRepository.get_microtopic_understanding(student.id, subject_id)
                for microtopic_number, stats in microtopic_stats.items():
                    if microtopic_number not in topics_stats:
                        topics_stats[microtopic_number] = []
                    topics_stats[microtopic_number].append(stats['percentage'])

        # Вычисляем средние значения по номерам микротем
        microtopic_data = {}
        for microtopic_number, percentages in topics_stats.items():
            if percentages:
                microtopic_data[microtopic_number] = round(sum(percentages) / len(percentages), 1)

        # Получаем названия микротем
        microtopics = await MicrotopicRepository.get_by_subject(subject_id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}

        return microtopic_data, microtopic_names, subject.name

    except Exception as e:
        print(f"Ошибка при получении данных микротем предмета: {e}")
        return {}, {}, "Ошибка загрузки"


async def show_subject_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    subject_id: int,                 # ID предмета
    role: str,                       # "manager", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "manager_subject_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    display_mode: str = "detailed",  # "detailed" или "summary"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Статистика по микротемам предмета",
    premium_check: bool = False      # Проверка премиума
):
    """
    Универсальная функция для отображения статистики предмета по микротемам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        subject_id: ID предмета
        role: Роль пользователя
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        display_mode: Режим отображения ("detailed" или "summary")
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                await show_premium_required_message(callback, "detailed_analytics")
                return

        # Получаем данные микротем предмета
        microtopic_data, microtopic_names, subject_name = await get_subject_microtopics_data(subject_id)

        if not microtopic_data:
            await callback.message.edit_text(
                f"📚 {subject_name}\n❌ Пока не выполнено ни одного задания по микротемам этого предмета",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем заголовок с названием предмета
        if display_mode == "detailed":
            dynamic_title = f"📚 Предмет: {subject_name}\n📈 Средний % понимания по микротемам"
        else:
            dynamic_title = f"📚 Предмет: {subject_name}\n🟢🔴 Сильные и слабые темы"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=dynamic_title,
            display_mode=display_mode,
            data_source=role,
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"subject_microtopics_{display_mode}.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb
        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None
            )
        )

        # Устанавливаем состояние
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode=display_mode,
            role=role,
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID предмета для возврата
            selected_subject_id=subject_id
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем предмета: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def show_subject_detailed_microtopics_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    role: str,                       # "manager", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "manager_subject_microtopics_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Детальная статистика по микротемам предмета",
    premium_check: bool = False      # Нужна ли проверка премиума
):
    """
    Универсальная функция для отображения детальной статистики по микротемам предмета
    Извлекает subject_id из callback_data

    Формат callback_data: subject_microtopics_detailed_SUBJECT_ID
    """
    # Извлекаем subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            subject_id = int(parts[3])

            await show_subject_microtopics_universal(
                callback=callback,
                state=state,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="detailed",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_subject_summary_microtopics_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    role: str,                       # "manager", "teacher", etc.
    target_state,                    # Состояние для установки
    callback_prefix: str,            # "manager_subject_summary_page"
    back_keyboard_func,              # Функция для кнопки "Назад"
    items_per_page: int = 15,        # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам предмета",
    premium_check: bool = False      # Нужна ли проверка премиума
):
    """
    Универсальная функция для отображения сводки по сильным и слабым темам предмета
    Извлекает subject_id из callback_data

    Формат callback_data: subject_microtopics_summary_SUBJECT_ID
    """
    # Извлекаем subject_id из callback_data
    parts = callback.data.split("_")
    if len(parts) >= 4:
        try:
            subject_id = int(parts[3])

            await show_subject_microtopics_universal(
                callback=callback,
                state=state,
                subject_id=subject_id,
                role=role,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="summary",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def back_from_subject_microtopics_to_analytics(callback: CallbackQuery, state: FSMContext, role: str):
    """
    Универсальная функция возврата из изображений микротем предмета к статистике предмета

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        role: Роль пользователя
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        subject_id = data.get('selected_subject_id')

        if not subject_id:
            # Если нет сохраненного ID предмета, возвращаемся к списку предметов
            if role == "manager":
                # Для менеджера используем его собственную функцию выбора предмета
                from manager.handlers.analytics import manager_select_subject
                await manager_select_subject(callback, state)
            else:
                from common.analytics.handlers import select_subject_for_analytics
                await select_subject_for_analytics(callback, state, role)
            return

        # Удаляем сообщение с изображением
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        # Получаем данные предмета и формируем сообщение напрямую
        from common.statistics import get_subject_stats
        from common.analytics.keyboards import get_subject_microtopics_kb

        subject_data = await get_subject_stats(subject_id)

        # Формируем базовую информацию о предмете
        result_text = f"📚 Предмет: {subject_data['name']}\n\n"
        result_text += f"👨‍👩‍👧‍👦 Количество групп: {len(subject_data['groups'])}\n"

        if subject_data['groups']:
            # Вычисляем взвешенный средний процент выполнения ДЗ
            total_weighted_homework = 0
            total_students = 0

            for group in subject_data['groups']:
                students_count = group.get('students_count', len(group.get('rating', [])))
                if students_count > 0:
                    total_weighted_homework += group['homework_completion'] * students_count
                    total_students += students_count

            avg_homework = total_weighted_homework / total_students if total_students > 0 else 0
            result_text += f"📊 Средний % выполнения ДЗ: {avg_homework:.1f}%\n"

            # Добавляем средний рост по тест-отчетам
            if subject_data.get('test_reports_growth') is not None:
                growth_value = subject_data['test_reports_growth']
                if growth_value >= 0:
                    result_text += f"📈 Средний % роста по тест-отчетам: +{growth_value}%\n\n"
                else:
                    result_text += f"📉 Средний % роста по тест-отчетам: {growth_value}%\n\n"
            else:
                result_text += f"📈 Средний % роста по тест-отчетам: Н/Д\n\n"

            # Показываем список групп
            result_text += "📋 Группы:\n"
            for group in subject_data['groups']:
                group_text = f"• {group['name']} - ДЗ: {group['homework_completion']}%"
                if group.get('test_reports_growth') is not None:
                    growth = group['test_reports_growth']
                    if growth >= 0:
                        group_text += f" | Рост: +{growth}%"
                    else:
                        group_text += f" | Рост: {growth}%"
                else:
                    group_text += f" | Рост: Н/Д"
                group_text += "\n"
                result_text += group_text
        else:
            result_text += "❌ Группы не найдены\n"

        result_text += "\nВыберите, что хотите посмотреть:"

        # Отправляем новое сообщение
        await callback.message.answer(
            result_text,
            reply_markup=get_subject_microtopics_kb(int(subject_id))
        )

        # Устанавливаем правильное состояние в зависимости от роли
        if role == "manager":
            from manager.handlers.analytics import ManagerAnalyticsStates
            await state.set_state(ManagerAnalyticsStates.subject_stats)
        elif role == "teacher":
            from teacher.handlers.analytics import TeacherAnalyticsStates
            await state.set_state(TeacherAnalyticsStates.subject_stats)

    except Exception as e:
        print(f"Ошибка при возврате из микротем предмета: {e}")
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        from common.analytics.keyboards import get_analytics_menu_kb
        await callback.message.answer(
            "📊 Аналитика\n\nВыберите тип статистики:",
            reply_markup=get_analytics_menu_kb(role)
        )

        # Устанавливаем главное состояние аналитики
        if role == "manager":
            from manager.handlers.analytics import ManagerAnalyticsStates
            await state.set_state(ManagerAnalyticsStates.main)
        elif role == "teacher":
            from teacher.handlers.analytics import TeacherAnalyticsStates
            await state.set_state(TeacherAnalyticsStates.main)


async def show_month_entry_test_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    test_result_id: int,              # ID результата входного теста месяца
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_entry_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    display_mode: str = "detailed",   # "detailed" или "summary"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Статистика входного теста месяца",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения статистики входного теста месяца по микротемам

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        test_result_id: ID результата входного теста месяца
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        display_mode: Режим отображения ("detailed" или "summary")
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
    """
    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                await show_premium_required_message(callback, "detailed_analytics")
                return

        # Получаем все данные оптимизированно
        from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
        from database.repositories.month_test_repository import MonthTestRepository
        from database.repositories.microtopic_repository import MicrotopicRepository
        from database.repositories.subject_repository import SubjectRepository
        import asyncio

        test_result = await MonthEntryTestResultRepository.get_by_id(test_result_id)
        if not test_result:
            await callback.message.edit_text(
                "❌ Результат теста не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Параллельно получаем все данные
        try:
            month_test_task = MonthTestRepository.get_by_id(test_result.month_test_id)
            microtopic_data_task = MonthEntryTestResultRepository.get_microtopic_statistics(test_result_id)

            month_test, microtopic_data = await asyncio.gather(month_test_task, microtopic_data_task)

            if not month_test:
                await callback.message.edit_text(
                    "❌ Тест месяца не найден",
                    reply_markup=back_keyboard_func()
                )
                return

            # Параллельно получаем предмет и микротемы
            subject_task = SubjectRepository.get_by_id(month_test.subject_id)
            microtopics_task = MicrotopicRepository.get_by_subject(month_test.subject_id)

            subject, microtopics = await asyncio.gather(subject_task, microtopics_task)

            if not subject:
                await callback.message.edit_text(
                    "❌ Предмет не найден",
                    reply_markup=back_keyboard_func()
                )
                return

            microtopic_names = {mt.number: mt.name for mt in microtopics}

        except Exception as e:
            print(f"Ошибка при параллельном получении данных входного теста: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при загрузке данных",
                reply_markup=back_keyboard_func()
            )
            return

        if not microtopic_data:
            await callback.message.edit_text(
                f"📊 Входной тест месяца: {month_test.name}\n📗 {subject.name}\n\n❌ Нет данных по микротемам",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем заголовок
        if display_mode == "detailed":
            dynamic_title = f"📊 Входной тест месяца: {month_test.name}\n📗 {subject.name}\n📈 % правильных ответов по микротемам"
        else:
            dynamic_title = f"📊 Входной тест месяца: {month_test.name}\n📗 {subject.name}\n🟢🔴 Сильные и слабые темы"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(microtopic_data)

        image_bytes = await generate_microtopics_table_image(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            title=dynamic_title,
            display_mode=display_mode,
            data_source="test",
            page=page,
            items_per_page=items_per_page
        )

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"month_entry_test_{display_mode}.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb

        # Используем универсальный callback для всех ролей
        back_callback_data = "back_to_month_entry_result"



        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None,
                back_callback_data=back_callback_data
            )
        )

        # Устанавливаем состояние
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=microtopic_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode=display_mode,
            role="student",
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID результата теста для возврата
            month_entry_test_result_id=test_result_id
        )

    except Exception as e:
        print(f"Ошибка при генерации изображения микротем входного теста месяца: {e}")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def show_month_entry_test_detailed_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_entry_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Детальная статистика входного теста месяца",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения детальной статистики входного теста месяца
    Извлекает test_result_id из callback_data

    Формат callback_data: student_month_entry_detailed_TEST_RESULT_ID
    """
    # Извлекаем test_result_id из callback_data
    # Формат: student_month_entry_detailed_TEST_RESULT_ID
    parts = callback.data.split("_")
    if len(parts) >= 5:
        try:
            test_result_id = int(parts[4])

            await show_month_entry_test_microtopics_universal(
                callback=callback,
                state=state,
                test_result_id=test_result_id,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="detailed",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_month_entry_test_summary_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_entry_summary_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам входного теста",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения сводки входного теста месяца
    Извлекает test_result_id из callback_data

    Формат callback_data: student_month_entry_summary_TEST_RESULT_ID
    """
    # Извлекаем test_result_id из callback_data
    # Формат: student_month_entry_summary_TEST_RESULT_ID
    parts = callback.data.split("_")
    if len(parts) >= 5:
        try:
            test_result_id = int(parts[4])

            await show_month_entry_test_microtopics_universal(
                callback=callback,
                state=state,
                test_result_id=test_result_id,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="summary",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def back_from_month_entry_test_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата из изображений микротем входного теста к результату теста

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        test_result_id = data.get('month_entry_test_result_id')

        if not test_result_id:
            # Если нет сохраненного ID результата теста, возвращаемся к меню тестов
            from common.student_tests.keyboards import get_back_to_test_kb
            await callback.message.delete()
            await callback.message.answer(
                "🧠 Тест-отчет\n\nВыберите тип теста:",
                reply_markup=get_back_to_test_kb()
            )
            return

        # Удаляем сообщение с изображением
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        # Получаем данные результата теста и формируем сообщение
        from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
        from database.repositories.month_test_repository import MonthTestRepository
        from database.repositories.subject_repository import SubjectRepository

        test_result = await MonthEntryTestResultRepository.get_by_id(test_result_id)
        if not test_result:
            from common.student_tests.keyboards import get_back_to_test_kb
            await callback.message.answer(
                "❌ Результат теста не найден",
                reply_markup=get_back_to_test_kb()
            )
            return

        month_test = await MonthTestRepository.get_by_id(test_result.month_test_id)
        subject = await SubjectRepository.get_by_id(month_test.subject_id) if month_test else None

        # Формируем сообщение с результатом теста
        result_text = f"📊 Результат входного теста месяца\n\n"
        if subject:
            result_text += f"📗 {subject.name}\n"
        if month_test:
            result_text += f"Тест: {month_test.name}\n"
        result_text += f"Верных: {test_result.correct_answers} / {test_result.total_questions}\n"
        result_text += f"Процент: {test_result.score_percentage}%\n\n"
        result_text += "Выберите тип аналитики:"

        # Создаем кнопки для детальной аналитики
        from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
        from common.student_tests.keyboards import get_back_to_test_kb

        buttons = [
            [InlineKeyboardButton(
                text="📊 Проценты по микротемам",
                callback_data=f"student_month_entry_detailed_{test_result_id}"
            )],
            [InlineKeyboardButton(
                text="💪 Сильные/слабые темы",
                callback_data=f"student_month_entry_summary_{test_result_id}"
            )]
        ]
        buttons.extend(get_back_to_test_kb().inline_keyboard)

        # Отправляем новое сообщение
        await callback.message.answer(
            result_text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
        )

        # Устанавливаем состояние результата теста
        from common.student_tests.states import StudentTestsStates
        await state.set_state(StudentTestsStates.month_entry_result)

        # Сохраняем ID результата теста для навигации
        await state.update_data(month_entry_test_result_id=test_result_id)

    except Exception as e:
        print(f"Ошибка при возврате из микротем входного теста: {e}")
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        from common.student_tests.keyboards import get_back_to_test_kb
        await callback.message.answer(
            "🧠 Тест-отчет\n\nВыберите тип теста:",
            reply_markup=get_back_to_test_kb()
        )


async def show_month_control_test_microtopics_universal(
    callback: CallbackQuery,
    state: FSMContext,
    test_result_id: int,              # ID результата контрольного теста месяца
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_control_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    display_mode: str = "detailed",   # "detailed" или "summary"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Статистика контрольного теста месяца",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения статистики контрольного теста месяца по микротемам с сравнением

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
        test_result_id: ID результата контрольного теста месяца
        target_state: Состояние для установки
        callback_prefix: Префикс для callback пагинации
        back_keyboard_func: Функция для генерации кнопки "Назад"
        display_mode: Режим отображения ("detailed" или "summary")
        items_per_page: Количество элементов на страницу
        caption: Подпись к изображению
        premium_check: Нужна ли проверка премиума
    """
    import time
    start_time = time.time()

    try:
        # Проверяем доступ к премиум функциям
        if premium_check:
            from common.premium_access import check_premium_access, show_premium_required_message

            has_premium = await check_premium_access(callback.from_user.id)
            if not has_premium:
                await show_premium_required_message(callback, "detailed_analytics")
                return

        # Получаем все данные оптимизированно
        from database.repositories.month_control_test_result_repository import MonthControlTestResultRepository
        from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
        from database.repositories.month_test_repository import MonthTestRepository
        from database.repositories.microtopic_repository import MicrotopicRepository
        from database.repositories.subject_repository import SubjectRepository
        import asyncio

        # Получаем результат контрольного теста с предзагрузкой связанных данных
        control_result = await MonthControlTestResultRepository.get_by_id(test_result_id)
        if not control_result:
            await callback.message.edit_text(
                "❌ Результат контрольного теста не найден",
                reply_markup=back_keyboard_func()
            )
            return

        # Параллельно получаем все остальные данные
        try:
            # Запускаем все запросы параллельно
            month_test_task = MonthTestRepository.get_by_id(control_result.month_test_id)
            entry_result_task = MonthEntryTestResultRepository.get_by_student_and_month_test(
                control_result.student_id, control_result.month_test_id
            )

            # Ждем завершения основных запросов
            month_test, entry_result = await asyncio.gather(month_test_task, entry_result_task)

            if not month_test:
                await callback.message.edit_text(
                    "❌ Тест месяца не найден",
                    reply_markup=back_keyboard_func()
                )
                return

            if not entry_result:
                await callback.message.edit_text(
                    f"❌ Входной тест для сравнения не найден",
                    reply_markup=back_keyboard_func()
                )
                return

            # Параллельно получаем предмет, микротемы и статистику
            subject_task = SubjectRepository.get_by_id(month_test.subject_id)
            microtopics_task = MicrotopicRepository.get_by_subject(month_test.subject_id)
            entry_stats_task = MonthEntryTestResultRepository.get_microtopic_statistics(entry_result.id)
            control_stats_task = MonthControlTestResultRepository.get_microtopic_statistics(control_result.id)

            # Ждем завершения всех запросов
            subject, microtopics, entry_stats, control_stats = await asyncio.gather(
                subject_task, microtopics_task, entry_stats_task, control_stats_task
            )

            if not subject:
                await callback.message.edit_text(
                    "❌ Предмет не найден",
                    reply_markup=back_keyboard_func()
                )
                return

            microtopic_names = {mt.number: mt.name for mt in microtopics}

        except Exception as e:
            print(f"Ошибка при параллельном получении данных: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при загрузке данных",
                reply_markup=back_keyboard_func()
            )
            return

        if not control_stats:
            await callback.message.edit_text(
                f"📊 Контрольный тест месяца: {month_test.name}\n📗 {subject.name}\n\n❌ Нет данных по микротемам",
                reply_markup=back_keyboard_func()
            )
            return

        # Формируем данные для сравнения в формате для изображений
        comparison_data = {}
        all_microtopics = set(entry_stats.keys()) | set(control_stats.keys())

        for microtopic_num in all_microtopics:
            entry_percentage = entry_stats.get(microtopic_num, {}).get('percentage', 0)
            control_percentage = control_stats.get(microtopic_num, {}).get('percentage', 0)

            # Формируем строку сравнения для отображения
            comparison_text = f"{entry_percentage}% → {control_percentage}%"

            # Добавляем эмодзи изменения
            if entry_percentage > 0:
                change = control_percentage - entry_percentage
                if change > 0:
                    comparison_text += " 📈"
                elif change < 0:
                    comparison_text += " 📉"
                else:
                    comparison_text += " ➡️"
            else:
                # Если входного теста не было по этой микротеме, показываем только контрольный
                if control_percentage >= 80:
                    comparison_text = f"{control_percentage}% ✅"
                elif control_percentage <= 40:
                    comparison_text = f"{control_percentage}% ❌"
                else:
                    comparison_text = f"{control_percentage}% ⚠️"

            comparison_data[microtopic_num] = {
                'comparison_text': comparison_text,
                'control_percentage': control_percentage,  # Для сортировки
                'entry_percentage': entry_percentage
            }

        # Формируем заголовок
        if display_mode == "detailed":
            dynamic_title = f"📊 Контрольный тест месяца: {month_test.name}\n📗 {subject.name}\n📈 Сравнение с входным тестом"
        else:
            dynamic_title = f"📊 Контрольный тест месяца: {month_test.name}\n📗 {subject.name}\n🟢🔴 Сильные и слабые темы (сравнение)"

        # Генерируем изображение таблицы (первая страница)
        page = 0
        total_items = len(comparison_data)

        # Оптимизированная генерация изображения
        try:
            image_bytes = await generate_microtopics_table_image(
                microtopic_data=comparison_data,
                microtopic_names=microtopic_names,
                title=dynamic_title,
                display_mode=display_mode,
                data_source="control_test",  # Новый тип источника данных
                page=page,
                items_per_page=items_per_page
            )
        except Exception as e:
            print(f"Ошибка при генерации изображения: {e}")
            await callback.message.edit_text(
                "❌ Ошибка при генерации изображения",
                reply_markup=back_keyboard_func()
            )
            return

        # Отправляем изображение
        photo = BufferedInputFile(image_bytes, filename=f"month_control_test_{display_mode}.png")
        await callback.message.delete()

        from .keyboards import get_microtopics_pagination_kb

        # Используем универсальный callback для всех ролей контрольного теста месяца
        back_callback_data = "back_to_month_control_result"

        await callback.message.answer_photo(
            photo=photo,
            caption=caption,
            reply_markup=get_microtopics_pagination_kb(
                current_page=page,
                total_items=total_items,
                items_per_page=items_per_page,
                callback_prefix=callback_prefix,
                back_keyboard_func=None,
                back_callback_data=back_callback_data
            )
        )

        # Устанавливаем состояние
        await state.set_state(target_state)

        # Сохраняем данные для навигации и пагинации
        await state.update_data(
            microtopic_data=comparison_data,
            microtopic_names=microtopic_names,
            display_title=dynamic_title,
            current_page=page,
            items_per_page=items_per_page,
            total_items=total_items,
            display_mode=display_mode,
            role="student",
            callback_prefix=callback_prefix,
            caption=caption,
            # Сохраняем ID результата теста для возврата
            month_control_test_result_id=test_result_id
        )

        # Логируем время выполнения
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⚡ Время выполнения show_month_control_test_microtopics_universal: {execution_time:.2f}с")

    except Exception as e:
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"❌ Ошибка при генерации изображения микротем контрольного теста месяца: {e} (время: {execution_time:.2f}с)")
        await callback.message.edit_text(
            "❌ Ошибка при загрузке статистики",
            reply_markup=back_keyboard_func()
        )


async def show_month_control_test_detailed_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_control_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Детальная статистика контрольного теста месяца",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения детальной статистики контрольного теста месяца
    Извлекает test_result_id из callback_data

    Формат callback_data: student_month_control_detailed_TEST_RESULT_ID
    """
    # Извлекаем test_result_id из callback_data
    # Формат: student_month_control_page_0_TEST_RESULT_ID
    parts = callback.data.split("_")
    if len(parts) >= 6:
        try:
            test_result_id = int(parts[5])

            await show_month_control_test_microtopics_universal(
                callback=callback,
                state=state,
                test_result_id=test_result_id,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="detailed",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def show_month_control_test_summary_from_callback(
    callback: CallbackQuery,
    state: FSMContext,
    target_state,                     # Состояние для установки
    callback_prefix: str,             # "student_month_control_summary_page"
    back_keyboard_func,               # Функция для кнопки "Назад"
    items_per_page: int = 15,         # Элементов на страницу
    caption: str = "📊 Сводка по сильным и слабым темам контрольного теста",
    premium_check: bool = False       # Проверка премиума
):
    """
    Универсальная функция для отображения сводки контрольного теста месяца
    Извлекает test_result_id из callback_data

    Формат callback_data: student_month_control_summary_TEST_RESULT_ID
    """
    # Извлекаем test_result_id из callback_data
    # Формат: student_month_control_summary_TEST_RESULT_ID
    parts = callback.data.split("_")
    if len(parts) >= 5:
        try:
            test_result_id = int(parts[4])

            await show_month_control_test_microtopics_universal(
                callback=callback,
                state=state,
                test_result_id=test_result_id,
                target_state=target_state,
                callback_prefix=callback_prefix,
                back_keyboard_func=back_keyboard_func,
                display_mode="summary",
                items_per_page=items_per_page,
                caption=caption,
                premium_check=premium_check
            )
        except ValueError:
            await callback.message.edit_text(
                "❌ Ошибка в данных запроса",
                reply_markup=back_keyboard_func()
            )
    else:
        await callback.message.edit_text(
            "❌ Ошибка в данных запроса",
            reply_markup=back_keyboard_func()
        )


async def back_from_month_control_test_microtopics_to_result(callback: CallbackQuery, state: FSMContext):
    """
    Универсальная функция возврата из изображений микротем контрольного теста к результату теста

    Args:
        callback: Объект CallbackQuery
        state: Контекст состояния FSM
    """
    try:
        # Получаем сохраненные данные
        data = await state.get_data()
        test_result_id = data.get('month_control_test_result_id')

        if not test_result_id:
            # Если нет сохраненного ID результата теста, возвращаемся к меню тестов
            from common.student_tests.keyboards import get_back_to_test_kb
            await callback.message.delete()
            await callback.message.answer(
                "🧠 Тест-отчет\n\nВыберите тип теста:",
                reply_markup=get_back_to_test_kb()
            )
            return

        # Удаляем сообщение с изображением
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        # Получаем данные результата теста и формируем сообщение
        from database.repositories.month_control_test_result_repository import MonthControlTestResultRepository
        from database.repositories.month_entry_test_result_repository import MonthEntryTestResultRepository
        from database.repositories.month_test_repository import MonthTestRepository
        from database.repositories.subject_repository import SubjectRepository

        control_result = await MonthControlTestResultRepository.get_by_id(test_result_id)
        if not control_result:
            from common.student_tests.keyboards import get_back_to_test_kb
            await callback.message.answer(
                "❌ Результат контрольного теста не найден",
                reply_markup=get_back_to_test_kb()
            )
            return

        month_test = await MonthTestRepository.get_by_id(control_result.month_test_id)
        subject = await SubjectRepository.get_by_id(month_test.subject_id) if month_test else None

        # Получаем входной тест для сравнения
        entry_result = await MonthEntryTestResultRepository.get_by_student_and_month_test(
            control_result.student_id, control_result.month_test_id
        )

        # Формируем сообщение с результатом теста
        result_text = f"📊 Результат контрольного теста месяца\n\n"
        if subject:
            result_text += f"📗 {subject.name}\n"
        if month_test:
            result_text += f"Тест: {month_test.name}\n"

        if entry_result:
            result_text += f"Верных: {entry_result.correct_answers} / {entry_result.total_questions} → {control_result.correct_answers} / {control_result.total_questions}\n"
            result_text += f"Процент: {entry_result.score_percentage}% → {control_result.score_percentage}%\n\n"
        else:
            result_text += f"Верных: {control_result.correct_answers} / {control_result.total_questions}\n"
            result_text += f"Процент: {control_result.score_percentage}%\n\n"

        result_text += "Выберите тип аналитики:"

        # Создаем кнопки для детальной аналитики
        from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
        from common.student_tests.keyboards import get_back_to_test_kb

        buttons = [
            [InlineKeyboardButton(
                text="📊 Проценты по микротемам",
                callback_data=f"student_month_control_detailed_{test_result_id}"
            )],
            [InlineKeyboardButton(
                text="💪 Сильные/слабые темы",
                callback_data=f"student_month_control_summary_{test_result_id}"
            )]
        ]
        buttons.extend(get_back_to_test_kb().inline_keyboard)

        # Отправляем новое сообщение
        await callback.message.answer(
            result_text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons)
        )

        # Устанавливаем состояние результата теста
        from common.student_tests.states import StudentTestsStates
        await state.set_state(StudentTestsStates.month_control_result)

        # Сохраняем ID результата теста для навигации
        await state.update_data(month_control_test_result_id=test_result_id)

    except Exception as e:
        print(f"Ошибка при возврате из микротем контрольного теста: {e}")
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            print(f"Не удалось удалить сообщение: {delete_error}")

        from common.student_tests.keyboards import get_back_to_test_kb
        await callback.message.answer(
            "🧠 Тест-отчет\n\nВыберите тип теста:",
            reply_markup=get_back_to_test_kb()
        )
